﻿using Autofac.Extras.DynamicProxy;
using Snp.Cache;
using Snp.ePort.Common;
using Snp.ePort.Gateway;
using Snp.ePort.Web.Infrastructure.Services;
using Snp.ePort.Web.Models.MSDS;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace Snp.ePort.Web.Controllers
{
    [Intercept("log-calls")]
    public class MsdsController : CommonController
    {
        private readonly IMSDSGatewayService _msdsGatewayService;
        private readonly IConfigGateway _configGateway;

        public MsdsController(ICacheManager cacheManager,
          IConfigGateway configGateway) : base(cacheManager)
        {
            _msdsGatewayService = new MSDSGatewayService();
            _configGateway = configGateway;
        }

        // GET: MSDS
        public ActionResult Index()
        {
            // Tạo dữ liệu mẫu cho demo - trong thực tế sẽ lấy từ database
            var model = GetMSDSContainers();
            System.Diagnostics.Debug.WriteLine($"MSDS Index called, model type: {model.GetType().Name}, count: {model.Count}");

            var maxFileUpload =
                _configGateway.GetePortSetting(string.Empty, AmEportSettingKey.MsdsMaxFileUploadPerTime, string.Empty, string.Empty, string.Empty);
            var allowFileTypes =
                _configGateway.GetePortSetting(string.Empty, AmEportSettingKey.MsdsAllowFileTypes, string.Empty, string.Empty, string.Empty);
            var maxFileSize =
                _configGateway.GetePortSetting(string.Empty, AmEportSettingKey.MsdsMaxFileSize, string.Empty, string.Empty, string.Empty);

            ViewBag.maxFileUpload = maxFileUpload.VALUE;
            ViewBag.allowFileTypes = allowFileTypes.VALUE;
            ViewBag.maxFileSize = maxFileSize.VALUE;

            return View(model);
        }

        // GET: MSDS/Test
        public ActionResult Test()
        {
            System.Diagnostics.Debug.WriteLine("MSDS Test action called");
            return View();
        }

        // GET: MSDS/GetFilesByOrderDetail
        [HttpGet]
        public async Task<JsonResult> GetFilesByOrderDetail(string orderDetailNo)
        {
            try
            {
                if (string.IsNullOrEmpty(orderDetailNo))
                {
                    return Json(new { success = false, message = "OrderDetailNo is required" }, JsonRequestBehavior.AllowGet);
                }

                var files = await _msdsGatewayService.GetMSDSFilesByOrderDetailNoAsync(orderDetailNo);
                return Json(new { success = true, data = files }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }

        // GET: MSDS/GetConfig
        [HttpGet]
        public async Task<JsonResult> GetConfig()
        {
            try
            {
                var config = await _msdsGatewayService.GetMSDSConfigAsync();
                return Json(config, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }

        // POST: MSDS/DeleteFile
        [HttpPost]
        public async Task<JsonResult> DeleteFile(string fileId)
        {
            try
            {
                if (string.IsNullOrEmpty(fileId))
                {
                    return Json(new { success = false, message = "FileId is required" });
                }

                var result = await _msdsGatewayService.DeleteMSDSFileAsync(fileId);
                return Json(result);
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        private List<MSDSContainerViewModel> GetMSDSContainers()
        {
            // Trong thực tế, dữ liệu này sẽ được lấy từ database thông qua gateway
            // Hiện tại tạo dữ liệu mẫu để demo
            return new List<MSDSContainerViewModel>
            {
                new MSDSContainerViewModel
                {
                    ContainerNo = "AVCB1231232",
                    RegisterNo = "Z12622",
                    OrderDetailNo = "OD123",
                    BookingNo = "123BVC",
                    IMO = "5.2.5.1",
                    UNNO = "3316.3467",
                    IMO_UNNO_Count = 2,
                    MSDS_Count = 2
                },
                new MSDSContainerViewModel
                {
                    ContainerNo = "AVCB1231233",
                    RegisterNo = "Z12623",
                    OrderDetailNo = "OD124",
                    BookingNo = "123BVC",
                    IMO = "5.2.5.1",
                    UNNO = "3316.3467",
                    IMO_UNNO_Count = 2,
                    MSDS_Count = 3
                },
                new MSDSContainerViewModel
                {
                    ContainerNo = "AVCB1231234",
                    RegisterNo = "Z12624",
                    OrderDetailNo = "OD125",
                    BookingNo = "123BVC",
                    IMO = "5.2.5.1",
                    UNNO = "3316.3467",
                    IMO_UNNO_Count = 2,
                    MSDS_Count = 0
                },
                new MSDSContainerViewModel
                {
                    ContainerNo = "AVCB1231235",
                    RegisterNo = "Z12625",
                    OrderDetailNo = "OD126",
                    BookingNo = "123BVC",
                    IMO = "5.2.5.1",
                    UNNO = "3316.3467",
                    IMO_UNNO_Count = 2,
                    MSDS_Count = 0
                },
                new MSDSContainerViewModel
                {
                    ContainerNo = "AVCB1231236",
                    RegisterNo = "Z12626",
                    OrderDetailNo = "OD127",
                    BookingNo = "123BVC",
                    IMO = "5.2.5.1",
                    UNNO = "3316.3467",
                    IMO_UNNO_Count = 2,
                    MSDS_Count = 0
                },
                new MSDSContainerViewModel
                {
                    ContainerNo = "AVCB1231212",
                    RegisterNo = "Z12629",
                    OrderDetailNo = "OD128",
                    BookingNo = "456VBY",
                    IMO = "5.2.1.7",
                    UNNO = "3316.1058",
                    IMO_UNNO_Count = 2,
                    MSDS_Count = 0
                },
                new MSDSContainerViewModel
                {
                    ContainerNo = "AVCB1231215",
                    RegisterNo = "Z12633",
                    OrderDetailNo = "OD129",
                    BookingNo = "456VBY",
                    IMO = "2.6.5.8",
                    UNNO = "1054.9865",
                    IMO_UNNO_Count = 2,
                    MSDS_Count = 0
                }
            };
        }

        private MSDSUploadResponse ValidateUploadedFiles(HttpFileCollectionBase files, MSDSConfigGatewayInfo config)
        {
            var response = new MSDSUploadResponse { Success = true };

            // Kiểm tra số lượng file
            if (files.Count > config.MaxFilesPerContainer)
            {
                response.Success = false;
                response.Errors.Add($"Chỉ được chọn tối đa {config.MaxFilesPerContainer} file cùng lúc.");
                return response;
            }

            long totalSize = 0;
            var invalidFiles = new List<string>();

            for (int i = 0; i < files.Count; i++)
            {
                var file = files[i];
                if (file == null || file.ContentLength == 0) continue;

                // Kiểm tra định dạng file
                var extension = Path.GetExtension(file.FileName)?.ToLower();
                if (string.IsNullOrEmpty(extension) || !config.AllowedExtensions.Contains(extension))
                {
                    invalidFiles.Add(file.FileName);
                }

                totalSize += file.ContentLength;
            }

            // Kiểm tra định dạng file
            if (invalidFiles.Any())
            {
                response.Success = false;
                response.Errors.Add($"Hệ thống chỉ chấp nhận các định dạng file sau: {string.Join(", ", config.AllowedExtensions)}. Vui lòng kiểm tra định dạng trước khi tải lên.");
                return response;
            }

            // Kiểm tra tổng dung lượng
            if (totalSize > config.MaxTotalSizePerContainer)
            {
                response.Success = false;
                var maxSizeMB = (config.MaxTotalSizePerContainer / 1024 / 1024);
                response.Errors.Add($"Tổng dung lượng file MSDS cho 1 container tối đa {maxSizeMB}MB. Vui lòng kiểm tra trước khi tải lên.");
                return response;
            }

            return response;
        }

        private List<string> GetOrderDetailNosByContainer(string containerNo)
        {
            // Trong thực tế, sẽ query database để lấy orderDetailNo theo containerNo
            // Hiện tại return dữ liệu mẫu
            var containers = GetMSDSContainers();
            var container = containers.FirstOrDefault(c => c.ContainerNo == containerNo);

            if (container != null)
            {
                return new List<string> { container.OrderDetailNo };
            }

            return new List<string>();
        }
    }
}