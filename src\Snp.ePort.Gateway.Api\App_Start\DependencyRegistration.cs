﻿using Autofac;
using Autofac.Extras.DynamicProxy;
using Autofac.Integration.Mvc;
using Autofac.Integration.Wcf;
using Autofac.Integration.WebApi;
using Autofac.Multitenant;
using AutoMapper;
using Castle.DynamicProxy;
using Snp.ePort.Common;
using Snp.ePort.Gateway.Api.Infrastructure;
using Snp.ePort.Gateway.Api.Models;
using Snp.ePort.Gateway.Api.Services;
using Snp.Log.AutoWriteLog;
using Snp.Tos.Entity.DTO.EDI;
using Snp.Tos.Entity.DTO.EdiBooking;
using System;
using System.Linq;
using System.Reflection;
using System.ServiceModel;
using System.Web.Http;

namespace Snp.ePort.Gateway.Api
{
    public static class DependencyRegistration
    {
        public static void Register()
        {
            var builder = new ContainerBuilder();

            // Register your Web API controllers.
            builder.RegisterControllers(Assembly.GetExecutingAssembly()).EnableClassInterceptors(); //Register MVC Controllers
            builder.RegisterApiControllers(Assembly.GetExecutingAssembly()).EnableClassInterceptors();

            var configMapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<Edi, CreateEdiDto>();
                cfg.CreateMap<Edi, EdiResponse>();
                cfg.CreateMap<EBooking, EdiBookingCreateModel>().ReverseMap();
                cfg.CreateMap<EdiEmptyReceiv, EdiEmptyReceivResponse>();
            });
            builder.RegisterInstance<IMapper>(configMapper.CreateMapper());

            builder.RegisterType<EDIService>().As<IEDIService>()
                .InstancePerLifetimeScope()
                .EnableInterfaceInterceptors();

            builder.Register(c => new CallLogger(AppDomain.CurrentDomain.RelativeSearchPath)).Named<IInterceptor>("log-calls");

            builder.AddGateway();

            GlobalConfiguration.Configuration.DependencyResolver = new AutofacWebApiDependencyResolver(builder.Build());
        }

        public static void AddGateway(this ContainerBuilder builder)
        {
            string transId = CommonUtilities.GetTransId();
            string user = GatewayAuthInfor.USER;
            string pass = GatewayAuthInfor.PASSWORD;

            builder.Register(c =>
            {
                var bindingName = Utils.GetConfigValue(BINDING_KEY.ORDER_BINDING_KEY);
                var binding = new BasicHttpBinding(bindingName);
                var url = Utils.GetConfigValue(BINDING_KEY.ConfigGatewayURL);
                var endPoint = new EndpointAddress(url);
                return new ChannelFactory<IConfigGateway>(binding, endPoint).CreateChannel();
            })
            .UseWcfSafeRelease()
            .EnableInterfaceInterceptors()
            .InstancePerLifetimeScope();

            builder.Register(c =>
            {
                var listUrl = c.Resolve<IConfigGateway>().GetCfgSiteServices(Default.DefaultSite, ServiceType.gw, user, pass, transId);
                var item = listUrl.FirstOrDefault(x => x.SITE_ID.Trim() == "CTL" && x.SERVICE_NAME == BINDING_KEY.TOSGatewayURL);
                return Impl.SettingGateway.RegisterChannel<ITOSGateway>(
                    item.SITE_ID.Trim(),
                    item.SERVICE_URL,
                    item.BINDING_NAME).CreateChannel();
            }).As<ITOSGateway>()
                    .UseWcfSafeRelease().InstancePerLifetimeScope().EnableInterfaceInterceptors();

            builder.Register(c =>
            {
                var listUrl = c.Resolve<IConfigGateway>().GetCfgSiteServices(Default.DefaultSite, ServiceType.gw, user, pass, transId);
                var item = listUrl.FirstOrDefault(x => x.SITE_ID.Trim() == "CTL" && x.SERVICE_NAME == BINDING_KEY.AuthGatewayURL);
                return Impl.SettingGateway.RegisterChannel<IAuthGateway>(
                    item.SITE_ID.Trim(),
                    item.SERVICE_URL,
                    item.BINDING_NAME).CreateChannel();
            }).As<IAuthGateway>()
            .UseWcfSafeRelease().InstancePerLifetimeScope().EnableInterfaceInterceptors();

            builder.Register(c =>
            {
                var listUrl = c.Resolve<IConfigGateway>().GetCfgSiteServices("CTL", ServiceType.gw, user, pass, transId);
                var item = listUrl.FirstOrDefault(x => x.SITE_ID.Trim() == "CTL" && x.SERVICE_NAME == BINDING_KEY.TOSCategoryGatewayURL);
                return Impl.SettingGateway.RegisterChannel<ITOSCategoryGateway>(
                    item.SITE_ID.Trim(),
                    item.SERVICE_URL,
                    item.BINDING_NAME).CreateChannel();
            }).As<ITOSCategoryGateway>()
           .UseWcfSafeRelease().InstancePerLifetimeScope().EnableInterfaceInterceptors();
        }

        public static void AddGateWayPerSite(this MultitenantContainer mtc, IConfigGateway config)
        {
            string transId = CommonUtilities.GetTransId();
            string user = GatewayAuthInfor.USER;
            string pass = GatewayAuthInfor.PASSWORD;

            // add  CFG_SITE_SERVCE_URL to Cache when server start
            var sites = config.GetSiteActive(true, user, pass, transId);
            var configUrls = config.GetCfgSiteServices(Default.DefaultSite, ServiceType.gw, user, pass, transId);

            ClientFactory.ConfigureCacheGateWay(sites, configUrls);

            // register Multitenant for per site
            foreach (var item in sites)
            {
                mtc.ConfigureTenant(item.SITE_ID.Trim(), (builder) =>
                {
                    builder.Register(c =>
                    {
                        var configUrl = ClientFactory.GetServiceUrl(item.SITE_ID, BINDING_KEY.TOSGatewayURL, user, pass, transId);
                        return Impl.SettingGateway.RegisterChannel<ITOSGateway>(
                            item.SITE_ID.Trim(),
                            configUrl.SERVICE_URL,
                            configUrl.BINDING_NAME).CreateChannel();
                    }).As<ITOSGateway>()
                    .UseWcfSafeRelease().InstancePerLifetimeScope().EnableInterfaceInterceptors();

                    builder.Register(c =>
                    {
                        var configUrl = ClientFactory.GetServiceUrl(item.SITE_ID, BINDING_KEY.AuthGatewayURL, user, pass, transId);
                        return Impl.SettingGateway.RegisterChannel<IAuthGateway>(
                            item.SITE_ID.Trim(),
                            configUrl.SERVICE_URL,
                            configUrl.BINDING_NAME).CreateChannel();
                    }).As<IAuthGateway>()
                    .UseWcfSafeRelease().InstancePerLifetimeScope().EnableInterfaceInterceptors();
                });
            }
        }
    }
}